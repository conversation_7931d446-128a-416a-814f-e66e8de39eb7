use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::config::{
    endpoints::{EndpointConfig, validate_unique_names},
    logger::LoggerConfig,
};

#[derive(Debug, Deserialize, Serialize, Validate, Default)]
#[validate(schema(function = "validate_app_config"))]
pub struct AppConfig {
    #[validate(nested)]
    pub logger: LoggerConfig,
    #[validate(length(min = 1))]
    #[validate(nested)]
    pub endpoints: Vec<EndpointConfig>,
}

fn validate_app_config(app_config: &AppConfig) -> Result<(), validator::ValidationError> {
    validate_unique_names(&app_config.endpoints)
}
