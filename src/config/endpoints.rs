use std::time::Duration;

use serde::{Deserialize, Serialize};
use serde_with::{DurationSeconds, serde_as};
use validator::Validate;

use crate::common::client::config::ShredstreamClientConfig;

fn default_enabled() -> bool {
    true
}

fn default_cooldown_duration() -> Duration {
    Duration::from_secs(300)
}

#[serde_as]
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Validate)]
pub struct EndpointConfig {
    #[validate(length(min = 1))]
    pub name: String,
    #[validate(url)]
    pub url: String,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    #[serde(default = "default_cooldown_duration")]
    #[serde_as(as = "DurationSeconds<u64>")]
    pub cooldown_duration: Duration,
    #[serde(flatten)]
    #[validate(nested)]
    pub client_config: ShredstreamClientConfig,
}

impl EndpointConfig {
    pub fn to_client_config(&self) -> ShredstreamClientConfig {
        ShredstreamClientConfig {
            connect_timeout: self.client_config.connect_timeout,
            subscribe_timeout: self.client_config.subscribe_timeout,
            retry_config: self.client_config.retry_config.clone(),
            circuit_breaker_config: self.client_config.circuit_breaker_config.clone(),
            failure_callback: None,
            disconnect_callback: None,
            retry_attempt_callback: None,
            retry_success_callback: None,
        }
    }
}

pub fn validate_unique_names(endpoints: &[EndpointConfig]) -> Result<(), validator::ValidationError> {
    let mut names = std::collections::HashSet::new();

    for endpoint in endpoints {
        if !names.insert(&endpoint.name) {
            let mut error = validator::ValidationError::new("duplicate_name");
            error.message = Some(format!("Endpoint name '{}' is not unique", endpoint.name).into());
            return Err(error);
        }
    }

    Ok(())
}
