{"$message_type":"diagnostic","message":"associated function `create_client_config_with_callbacks` is private","code":{"code":"E0624","explanation":"A private item was used outside of its scope.\n\nErroneous code example:\n\n```compile_fail,E0624\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // error: method `method` is private\n```\n\nTwo possibilities are available to solve this issue:\n\n1. Only use the item in the scope it has been defined:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n\n    pub fn call_method(foo: &Foo) { // We create a public function.\n        foo.method(); // Which calls the item.\n    }\n}\n\nlet foo = inner::Foo;\ninner::call_method(&foo); // And since the function is public, we can call the\n                          // method through it.\n```\n\n2. Make the item public:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        pub fn method(&self) {} // It's now public.\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // Ok!\n```\n"},"level":"error","spans":[{"file_name":"tests/endpoint_manager_callbacks_test.rs","byte_start":555,"byte_end":590,"line_start":18,"line_end":18,"column_start":72,"column_end":107,"is_primary":true,"text":[{"text":"    let config = shreder::common::endpoints::manager::EndpointManager::create_client_config_with_callbacks(&endpoint_config);","highlight_start":72,"highlight_end":107}],"label":"private associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs","byte_start":3227,"byte_end":3326,"line_start":86,"line_end":86,"column_start":5,"column_end":104,"is_primary":false,"text":[{"text":"    fn create_client_config_with_callbacks(endpoint_config: &EndpointConfig) -> ShredstreamClientConfig {","highlight_start":5,"highlight_end":104}],"label":"private associated function defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0624]\u001b[0m\u001b[0m\u001b[1m: associated function `create_client_config_with_callbacks` is private\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/endpoint_manager_callbacks_test.rs:18:72\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = shreder::common::endpoints::manager::EndpointManager::create_client_config_with_callbacks(&endpoint_config);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate associated function\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs:86:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn create_client_config_with_callbacks(endpoint_config: &EndpointConfig) -> ShredstreamClientConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mprivate associated function defined here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `handle_disconnect_callback` is private","code":{"code":"E0624","explanation":"A private item was used outside of its scope.\n\nErroneous code example:\n\n```compile_fail,E0624\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // error: method `method` is private\n```\n\nTwo possibilities are available to solve this issue:\n\n1. Only use the item in the scope it has been defined:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n\n    pub fn call_method(foo: &Foo) { // We create a public function.\n        foo.method(); // Which calls the item.\n    }\n}\n\nlet foo = inner::Foo;\ninner::call_method(&foo); // And since the function is public, we can call the\n                          // method through it.\n```\n\n2. Make the item public:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        pub fn method(&self) {} // It's now public.\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // Ok!\n```\n"},"level":"error","spans":[{"file_name":"tests/endpoint_manager_callbacks_test.rs","byte_start":1166,"byte_end":1192,"line_start":34,"line_end":34,"column_start":59,"column_end":85,"is_primary":true,"text":[{"text":"    shreder::common::endpoints::manager::EndpointManager::handle_disconnect_callback(","highlight_start":59,"highlight_end":85}],"label":"private associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs","byte_start":4111,"byte_end":4213,"line_start":108,"line_end":108,"column_start":5,"column_end":107,"is_primary":false,"text":[{"text":"    fn handle_disconnect_callback(endpoint: &str, error: &ShredstreamError, delay: Duration, attempt: u32) {","highlight_start":5,"highlight_end":107}],"label":"private associated function defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0624]\u001b[0m\u001b[0m\u001b[1m: associated function `handle_disconnect_callback` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/endpoint_manager_callbacks_test.rs:34:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    shreder::common::endpoints::manager::EndpointManager::handle_disconnect_callback(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate associated function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs:108:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_disconnect_callback(endpoint: &str, error: &ShredstreamError, delay: Duration, attempt: u32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mprivate associated function defined here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `handle_retry_attempt_callback` is private","code":{"code":"E0624","explanation":"A private item was used outside of its scope.\n\nErroneous code example:\n\n```compile_fail,E0624\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // error: method `method` is private\n```\n\nTwo possibilities are available to solve this issue:\n\n1. Only use the item in the scope it has been defined:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n\n    pub fn call_method(foo: &Foo) { // We create a public function.\n        foo.method(); // Which calls the item.\n    }\n}\n\nlet foo = inner::Foo;\ninner::call_method(&foo); // And since the function is public, we can call the\n                          // method through it.\n```\n\n2. Make the item public:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        pub fn method(&self) {} // It's now public.\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // Ok!\n```\n"},"level":"error","spans":[{"file_name":"tests/endpoint_manager_callbacks_test.rs","byte_start":1348,"byte_end":1377,"line_start":41,"line_end":41,"column_start":59,"column_end":88,"is_primary":true,"text":[{"text":"    shreder::common::endpoints::manager::EndpointManager::handle_retry_attempt_callback(","highlight_start":59,"highlight_end":88}],"label":"private associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs","byte_start":4439,"byte_end":4520,"line_start":118,"line_end":118,"column_start":5,"column_end":86,"is_primary":false,"text":[{"text":"    fn handle_retry_attempt_callback(endpoint: &str, attempt: u32, max_attempts: u32) {","highlight_start":5,"highlight_end":86}],"label":"private associated function defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0624]\u001b[0m\u001b[0m\u001b[1m: associated function `handle_retry_attempt_callback` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/endpoint_manager_callbacks_test.rs:41:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    shreder::common::endpoints::manager::EndpointManager::handle_retry_attempt_callback(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate associated function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs:118:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_retry_attempt_callback(endpoint: &str, attempt: u32, max_attempts: u32) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mprivate associated function defined here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `handle_retry_success_callback` is private","code":{"code":"E0624","explanation":"A private item was used outside of its scope.\n\nErroneous code example:\n\n```compile_fail,E0624\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // error: method `method` is private\n```\n\nTwo possibilities are available to solve this issue:\n\n1. Only use the item in the scope it has been defined:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n\n    pub fn call_method(foo: &Foo) { // We create a public function.\n        foo.method(); // Which calls the item.\n    }\n}\n\nlet foo = inner::Foo;\ninner::call_method(&foo); // And since the function is public, we can call the\n                          // method through it.\n```\n\n2. Make the item public:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        pub fn method(&self) {} // It's now public.\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // Ok!\n```\n"},"level":"error","spans":[{"file_name":"tests/endpoint_manager_callbacks_test.rs","byte_start":1519,"byte_end":1548,"line_start":47,"line_end":47,"column_start":59,"column_end":88,"is_primary":true,"text":[{"text":"    shreder::common::endpoints::manager::EndpointManager::handle_retry_success_callback(","highlight_start":59,"highlight_end":88}],"label":"private associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs","byte_start":4644,"byte_end":4730,"line_start":122,"line_end":122,"column_start":5,"column_end":91,"is_primary":false,"text":[{"text":"    fn handle_retry_success_callback(endpoint: &str, attempt: u32, elapsed_time: Duration) {","highlight_start":5,"highlight_end":91}],"label":"private associated function defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0624]\u001b[0m\u001b[0m\u001b[1m: associated function `handle_retry_success_callback` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/endpoint_manager_callbacks_test.rs:47:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    shreder::common::endpoints::manager::EndpointManager::handle_retry_success_callback(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate associated function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs:122:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_retry_success_callback(endpoint: &str, attempt: u32, elapsed_time: Duration) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mprivate associated function defined here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `handle_failure_callback` is private","code":{"code":"E0624","explanation":"A private item was used outside of its scope.\n\nErroneous code example:\n\n```compile_fail,E0624\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // error: method `method` is private\n```\n\nTwo possibilities are available to solve this issue:\n\n1. Only use the item in the scope it has been defined:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n\n    pub fn call_method(foo: &Foo) { // We create a public function.\n        foo.method(); // Which calls the item.\n    }\n}\n\nlet foo = inner::Foo;\ninner::call_method(&foo); // And since the function is public, we can call the\n                          // method through it.\n```\n\n2. Make the item public:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        pub fn method(&self) {} // It's now public.\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // Ok!\n```\n"},"level":"error","spans":[{"file_name":"tests/endpoint_manager_callbacks_test.rs","byte_start":1690,"byte_end":1713,"line_start":53,"line_end":53,"column_start":59,"column_end":82,"is_primary":true,"text":[{"text":"    shreder::common::endpoints::manager::EndpointManager::handle_failure_callback(","highlight_start":59,"highlight_end":82}],"label":"private associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs","byte_start":4891,"byte_end":4959,"line_start":126,"line_end":126,"column_start":5,"column_end":73,"is_primary":false,"text":[{"text":"    fn handle_failure_callback(endpoint: &str, error: &ShredstreamError) {","highlight_start":5,"highlight_end":73}],"label":"private associated function defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0624]\u001b[0m\u001b[0m\u001b[1m: associated function `handle_failure_callback` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/endpoint_manager_callbacks_test.rs:53:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    shreder::common::endpoints::manager::EndpointManager::handle_failure_callback(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate associated function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/PJ/shreder/src/common/endpoints/manager.rs:126:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn handle_failure_callback(endpoint: &str, error: &ShredstreamError) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mprivate associated function defined here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 5 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 5 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0624`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0624`.\u001b[0m\n"}
